#!/usr/bin/env python3
"""
舵机测试程序 - 用于调试舵机连接和PWM功能
MaixCAM Pro 舵机控制测试
"""

from maix import app, time
from maix.peripheral import pwm

def test_pwm_init():
    """测试不同的PWM初始化方式"""
    print("=== 舵机PWM初始化测试 ===")
    
    vertical_pwm = None
    horizontal_pwm = None
    
    # 测试方式1：字符串标识符
    try:
        print("尝试方式1：字符串PWM标识符...")
        vertical_pwm = pwm.PWM("PWM7", freq=50, duty=7.5)
        horizontal_pwm = pwm.PWM("PWM6", freq=50, duty=7.5)
        print("✅ 方式1成功：PWM7(A19), PWM6(A18)")
        return vertical_pwm, horizontal_pwm
    except Exception as e:
        print(f"❌ 方式1失败: {e}")
    
    # 测试方式2：数字编号
    try:
        print("尝试方式2：数字PWM编号...")
        vertical_pwm = pwm.PWM(7, freq=50, duty=7.5)
        horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5)
        print("✅ 方式2成功：PWM7, PWM6")
        return vertical_pwm, horizontal_pwm
    except Exception as e:
        print(f"❌ 方式2失败: {e}")
    
    # 测试方式3：引脚编号
    try:
        print("尝试方式3：引脚编号...")
        vertical_pwm = pwm.PWM(19, freq=50, duty=7.5)
        horizontal_pwm = pwm.PWM(18, freq=50, duty=7.5)
        print("✅ 方式3成功：Pin19(A19), Pin18(A18)")
        return vertical_pwm, horizontal_pwm
    except Exception as e:
        print(f"❌ 方式3失败: {e}")
    
    print("❌ 所有PWM初始化方式都失败了")
    return None, None

def test_servo_movement(vertical_pwm, horizontal_pwm):
    """测试舵机运动"""
    if not vertical_pwm or not horizontal_pwm:
        print("PWM未初始化，跳过运动测试")
        return
    
    print("\n=== 舵机运动测试 ===")
    
    try:
        # 测试垂直舵机
        print("测试垂直舵机（180度）...")
        
        # 0度 (2.5% 占空比)
        print("设置0度...")
        vertical_pwm.duty(2.5)
        time.sleep(2)
        
        # 90度 (7.5% 占空比)
        print("设置90度...")
        vertical_pwm.duty(7.5)
        time.sleep(2)
        
        # 180度 (12.5% 占空比)
        print("设置180度...")
        vertical_pwm.duty(12.5)
        time.sleep(2)
        
        # 回中位
        print("回到90度中位...")
        vertical_pwm.duty(7.5)
        time.sleep(1)
        
        print("✅ 垂直舵机测试完成")
        
    except Exception as e:
        print(f"❌ 垂直舵机测试失败: {e}")
    
    try:
        # 测试水平舵机
        print("\n测试水平舵机（360度连续旋转）...")
        
        # 停止 (7.5% 占空比)
        print("停止...")
        horizontal_pwm.duty(7.5)
        time.sleep(1)
        
        # 顺时针 (10% 占空比)
        print("顺时针旋转...")
        horizontal_pwm.duty(10.0)
        time.sleep(3)
        
        # 停止
        print("停止...")
        horizontal_pwm.duty(7.5)
        time.sleep(1)
        
        # 逆时针 (5% 占空比)
        print("逆时针旋转...")
        horizontal_pwm.duty(5.0)
        time.sleep(3)
        
        # 停止
        print("停止...")
        horizontal_pwm.duty(7.5)
        time.sleep(1)
        
        print("✅ 水平舵机测试完成")
        
    except Exception as e:
        print(f"❌ 水平舵机测试失败: {e}")

def main():
    print("MaixCAM Pro 舵机测试程序")
    print("连接说明：")
    print("- 垂直舵机（180°）→ A19引脚")
    print("- 水平舵机（360°）→ A18引脚")
    print("- 红线 → 5V")
    print("- 黑线/棕线 → GND")
    print("- 橙线/黄线 → 信号线")
    print()
    
    # 初始化PWM
    vertical_pwm, horizontal_pwm = test_pwm_init()
    
    if vertical_pwm and horizontal_pwm:
        print("\n按任意键开始舵机运动测试，或Ctrl+C退出...")
        input()
        
        # 测试舵机运动
        test_servo_movement(vertical_pwm, horizontal_pwm)
        
        # 清理资源
        print("\n清理资源...")
        try:
            vertical_pwm.close()
            horizontal_pwm.close()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"❌ 资源清理失败: {e}")
    else:
        print("\n请检查：")
        print("1. MaixCAM Pro系统版本是否支持PWM")
        print("2. 引脚连接是否正确")
        print("3. 舵机电源是否正常")
        print("4. 查看MaixPy官方文档确认PWM API")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序异常: {e}")
